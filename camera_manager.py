"""
Camera Manager Module
Handles camera connections, streaming, and frame capture
Supports RTSP, IP, USB, and ONVIF cameras
"""

import cv2
import threading
import time
import os
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal, QMutex
import numpy as np

class CameraThread(QThread):
    """Thread for handling individual camera streams"""
    frame_ready = pyqtSignal(str, np.ndarray)  # camera_id, frame
    connection_status = pyqtSignal(str, bool)  # camera_id, connected
    
    def __init__(self, camera_config):
        super().__init__()
        self.camera_config = camera_config
        self.camera_id = camera_config['id']
        self.running = False
        self.cap = None
        self.mutex = QMutex()
        self.last_frame = None
        self.fps = 30
        self.frame_count = 0
        self.start_time = time.time()
        
    def run(self):
        """Main thread execution"""
        self.running = True
        self.connect_camera()
        
        while self.running:
            try:
                if self.cap and self.cap.isOpened():
                    ret, frame = self.cap.read()
                    if ret:
                        self.mutex.lock()
                        self.last_frame = frame.copy()
                        self.mutex.unlock()
                        
                        # Add overlay information
                        frame_with_overlay = self.add_overlay(frame)
                        
                        # Emit frame signal
                        self.frame_ready.emit(self.camera_id, frame_with_overlay)
                        
                        # Calculate FPS
                        self.frame_count += 1
                        if self.frame_count % 30 == 0:
                            elapsed = time.time() - self.start_time
                            self.fps = self.frame_count / elapsed if elapsed > 0 else 0
                        
                        # Control frame rate
                        time.sleep(1/30)  # 30 FPS
                    else:
                        # Try to reconnect
                        self.connection_status.emit(self.camera_id, False)
                        time.sleep(1)
                        self.reconnect_camera()
                else:
                    time.sleep(1)
                    self.reconnect_camera()
                    
            except Exception as e:
                print(f"Camera {self.camera_id} error: {e}")
                self.connection_status.emit(self.camera_id, False)
                time.sleep(2)
                self.reconnect_camera()
                
    def connect_camera(self):
        """Connect to camera based on type"""
        try:
            camera_type = self.camera_config['type']
            url = self.camera_config['url']
            
            if camera_type == 'usb':
                # USB camera
                camera_index = int(url)
                self.cap = cv2.VideoCapture(camera_index)
            elif camera_type in ['rtsp', 'ip', 'onvif']:
                # Network camera
                self.cap = cv2.VideoCapture(url)
            else:
                raise ValueError(f"Unsupported camera type: {camera_type}")
                
            if self.cap and self.cap.isOpened():
                # Set camera properties
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.cap.set(cv2.CAP_PROP_FPS, 30)
                
                # Test frame capture
                ret, frame = self.cap.read()
                if ret:
                    self.connection_status.emit(self.camera_id, True)
                    print(f"Camera {self.camera_id} connected successfully")
                else:
                    raise Exception("Failed to capture test frame")
            else:
                raise Exception("Failed to open camera")
                
        except Exception as e:
            print(f"Failed to connect camera {self.camera_id}: {e}")
            self.connection_status.emit(self.camera_id, False)
            if self.cap:
                self.cap.release()
                self.cap = None
                
    def reconnect_camera(self):
        """Attempt to reconnect camera"""
        if self.cap:
            self.cap.release()
            self.cap = None
        time.sleep(2)
        self.connect_camera()
        
    def add_overlay(self, frame):
        """Add overlay information to frame"""
        if frame is None:
            return frame
            
        overlay_frame = frame.copy()
        height, width = overlay_frame.shape[:2]
        
        # Camera name
        camera_name = self.camera_config['name']
        cv2.putText(overlay_frame, camera_name, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(overlay_frame, timestamp, (10, height - 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # FPS
        fps_text = f"FPS: {self.fps:.1f}"
        cv2.putText(overlay_frame, fps_text, (width - 100, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Recording indicator
        if self.camera_config.get('recording_enabled', False):
            cv2.circle(overlay_frame, (width - 30, 50), 8, (0, 0, 255), -1)
            cv2.putText(overlay_frame, "REC", (width - 50, 75), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        return overlay_frame
        
    def get_current_frame(self):
        """Get the current frame (thread-safe)"""
        self.mutex.lock()
        frame = self.last_frame.copy() if self.last_frame is not None else None
        self.mutex.unlock()
        return frame
        
    def stop(self):
        """Stop the camera thread"""
        self.running = False
        if self.cap:
            self.cap.release()
        self.wait()

class CameraManager:
    """Main camera management class"""
    
    def __init__(self):
        self.cameras = {}
        self.camera_threads = {}
        self.screenshots_path = "./screenshots"
        self.ensure_directories()
        
    def ensure_directories(self):
        """Ensure required directories exist"""
        os.makedirs(self.screenshots_path, exist_ok=True)
        
    def add_camera(self, camera_config):
        """Add a new camera"""
        camera_id = camera_config['id']
        
        if camera_id in self.camera_threads:
            self.remove_camera(camera_id)
            
        # Create camera thread
        camera_thread = CameraThread(camera_config)
        self.camera_threads[camera_id] = camera_thread
        self.cameras[camera_id] = camera_config
        
        print(f"Added camera: {camera_id} - {camera_config['name']}")
        
    def remove_camera(self, camera_id):
        """Remove a camera"""
        if camera_id in self.camera_threads:
            self.camera_threads[camera_id].stop()
            del self.camera_threads[camera_id]
            
        if camera_id in self.cameras:
            del self.cameras[camera_id]
            
        print(f"Removed camera: {camera_id}")
        
    def start_camera(self, camera_id):
        """Start a specific camera"""
        if camera_id in self.camera_threads:
            if not self.camera_threads[camera_id].isRunning():
                self.camera_threads[camera_id].start()
                print(f"Started camera: {camera_id}")
            
    def stop_camera(self, camera_id):
        """Stop a specific camera"""
        if camera_id in self.camera_threads:
            self.camera_threads[camera_id].stop()
            print(f"Stopped camera: {camera_id}")
            
    def start_all_cameras(self):
        """Start all cameras"""
        for camera_id in self.camera_threads:
            self.start_camera(camera_id)
            
    def stop_all_cameras(self):
        """Stop all cameras"""
        for camera_id in list(self.camera_threads.keys()):
            self.stop_camera(camera_id)
            
    def get_camera_frame(self, camera_id):
        """Get current frame from specific camera"""
        if camera_id in self.camera_threads:
            return self.camera_threads[camera_id].get_current_frame()
        return None
        
    def take_screenshot(self, camera_id):
        """Take screenshot from specific camera"""
        frame = self.get_camera_frame(camera_id)
        if frame is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            camera_name = self.cameras[camera_id]['name'].replace(' ', '_')
            filename = f"{camera_name}_{timestamp}.jpg"
            filepath = os.path.join(self.screenshots_path, filename)
            
            cv2.imwrite(filepath, frame)
            print(f"Screenshot saved: {filepath}")
            return filepath
        return None
        
    def take_screenshots(self):
        """Take screenshots from all active cameras"""
        screenshots = []
        for camera_id in self.cameras:
            if camera_id in self.camera_threads and self.camera_threads[camera_id].isRunning():
                screenshot_path = self.take_screenshot(camera_id)
                if screenshot_path:
                    screenshots.append(screenshot_path)
        return screenshots
        
    def get_camera_status(self, camera_id):
        """Get camera connection status"""
        if camera_id in self.camera_threads:
            return self.camera_threads[camera_id].isRunning()
        return False
        
    def get_all_camera_status(self):
        """Get status of all cameras"""
        status = {}
        for camera_id in self.cameras:
            status[camera_id] = self.get_camera_status(camera_id)
        return status
        
    def update_camera_config(self, camera_id, new_config):
        """Update camera configuration"""
        if camera_id in self.cameras:
            # Stop current camera
            self.stop_camera(camera_id)
            
            # Update configuration
            self.cameras[camera_id].update(new_config)
            
            # Restart with new config
            self.remove_camera(camera_id)
            self.add_camera(self.cameras[camera_id])
            self.start_camera(camera_id)
            
    def get_camera_info(self, camera_id):
        """Get detailed camera information"""
        if camera_id in self.cameras:
            info = self.cameras[camera_id].copy()
            info['status'] = self.get_camera_status(camera_id)
            if camera_id in self.camera_threads:
                info['fps'] = self.camera_threads[camera_id].fps
                info['frame_count'] = self.camera_threads[camera_id].frame_count
            return info
        return None
