"""
Setup script for Camera Monitoring System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="camera-monitoring-system",
    version="1.0.0",
    author="Camera Monitoring Solutions",
    author_email="<EMAIL>",
    description="Professional camera monitoring system with motion detection and recording",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/cameramonitoring/camera-monitoring-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Video :: Capture",
        "Topic :: Security",
        "Topic :: System :: Monitoring",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "camera-monitoring=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.txt", "*.ico"],
    },
    data_files=[
        ("", ["config.json", "cameras.json", "requirements.txt"]),
    ],
    keywords="camera monitoring surveillance security motion detection recording rtsp ip usb onvif",
    project_urls={
        "Bug Reports": "https://github.com/cameramonitoring/camera-monitoring-system/issues",
        "Source": "https://github.com/cameramonitoring/camera-monitoring-system",
        "Documentation": "https://github.com/cameramonitoring/camera-monitoring-system/wiki",
    },
)
