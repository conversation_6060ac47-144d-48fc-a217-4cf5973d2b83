"""
Test script for Camera Monitoring System
Verifies installation and basic functionality
"""

import sys
import os
import json
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    required_modules = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PyQt5.QtWidgets', 'PyQt5'),
        ('PyQt5.QtCore', 'PyQt5 Core'),
        ('PyQt5.QtGui', 'PyQt5 GUI'),
        ('PIL', 'Pillow'),
        ('imutils', 'imutils'),
    ]
    
    failed_imports = []
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"✓ {name} imported successfully")
        except ImportError as e:
            print(f"✗ Failed to import {name}: {e}")
            failed_imports.append(name)
    
    return len(failed_imports) == 0, failed_imports

def test_config_files():
    """Test if configuration files exist and are valid"""
    print("\nTesting configuration files...")
    
    config_files = ['config.json', 'cameras.json']
    
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    json.load(f)
                print(f"✓ {config_file} is valid")
            else:
                print(f"✗ {config_file} not found")
                return False
        except json.JSONDecodeError as e:
            print(f"✗ {config_file} is invalid JSON: {e}")
            return False
        except Exception as e:
            print(f"✗ Error reading {config_file}: {e}")
            return False
    
    return True

def test_directories():
    """Test if required directories can be created"""
    print("\nTesting directory creation...")
    
    required_dirs = ['recordings', 'screenshots', 'alerts']
    
    for directory in required_dirs:
        try:
            os.makedirs(directory, exist_ok=True)
            if os.path.exists(directory):
                print(f"✓ {directory} directory ready")
            else:
                print(f"✗ Failed to create {directory} directory")
                return False
        except Exception as e:
            print(f"✗ Error creating {directory}: {e}")
            return False
    
    return True

def test_opencv_functionality():
    """Test basic OpenCV functionality"""
    print("\nTesting OpenCV functionality...")
    
    try:
        import cv2
        import numpy as np
        
        # Test video capture creation (doesn't need actual camera)
        cap = cv2.VideoCapture()
        print("✓ VideoCapture object created")
        
        # Test image processing
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print("✓ Image processing works")
        
        # Test background subtractor
        bg_subtractor = cv2.createBackgroundSubtractorMOG2()
        print("✓ Background subtractor created")
        
        # Test video writer (without actually writing)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        print("✓ Video codec available")
        
        return True
        
    except Exception as e:
        print(f"✗ OpenCV functionality test failed: {e}")
        return False

def test_pyqt_functionality():
    """Test basic PyQt5 functionality"""
    print("\nTesting PyQt5 functionality...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import QTimer
        from PyQt5.QtGui import QPixmap, QImage
        
        # Test application creation (without showing)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test widget creation
        widget = QWidget()
        print("✓ QWidget created")
        
        # Test timer
        timer = QTimer()
        print("✓ QTimer created")
        
        # Test image handling
        image = QImage(100, 100, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(image)
        print("✓ Image/Pixmap handling works")
        
        return True
        
    except Exception as e:
        print(f"✗ PyQt5 functionality test failed: {e}")
        return False

def test_system_modules():
    """Test custom system modules"""
    print("\nTesting system modules...")
    
    modules_to_test = [
        'camera_manager',
        'video_recorder',
        'motion_detector',
        'settings_manager',
        'ui_components'
    ]
    
    for module_name in modules_to_test:
        try:
            module = __import__(module_name)
            print(f"✓ {module_name} imported successfully")
        except ImportError as e:
            print(f"✗ Failed to import {module_name}: {e}")
            return False
        except Exception as e:
            print(f"✗ Error in {module_name}: {e}")
            return False
    
    return True

def test_basic_functionality():
    """Test basic system functionality"""
    print("\nTesting basic functionality...")
    
    try:
        from settings_manager import SettingsManager
        from camera_manager import CameraManager
        from video_recorder import VideoRecorder
        from motion_detector import MotionDetector
        
        # Test settings manager
        settings = SettingsManager()
        config = settings.load_config()
        print("✓ Settings manager works")
        
        # Test camera manager
        cam_manager = CameraManager()
        print("✓ Camera manager created")
        
        # Test video recorder
        recorder = VideoRecorder()
        print("✓ Video recorder created")
        
        # Test motion detector
        detector = MotionDetector()
        print("✓ Motion detector created")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("Camera Monitoring System - System Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Files", test_config_files),
        ("Directory Creation", test_directories),
        ("OpenCV Functionality", test_opencv_functionality),
        ("PyQt5 Functionality", test_pyqt_functionality),
        ("System Modules", test_system_modules),
        ("Basic Functionality", test_basic_functionality),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, details = result
                if not success:
                    failed_tests.append((test_name, details))
            else:
                success = result
                if not success:
                    failed_tests.append((test_name, "Test failed"))
            
            if success:
                passed_tests += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
                
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
            failed_tests.append((test_name, str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {len(failed_tests)}")
    
    if failed_tests:
        print("\nFailed Tests:")
        for test_name, error in failed_tests:
            print(f"  - {test_name}: {error}")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! System is ready to use.")
        return True
    else:
        print(f"\n⚠️  {len(failed_tests)} test(s) failed. Please check the issues above.")
        return False

def main():
    """Main test function"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n" + "=" * 60)
            print("You can now run the application with: python main.py")
            print("Or build the executable with: python build_exe.py")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("Please fix the issues above before running the application.")
            print("You may need to install missing dependencies:")
            print("pip install -r requirements.txt")
            print("=" * 60)
        
        return success
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        return False
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
