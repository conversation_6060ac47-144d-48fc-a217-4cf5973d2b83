"""
Motion Detection Module
Implements motion detection using OpenCV
Supports configurable detection zones and sensitivity
"""

import cv2
import numpy as np
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
import json
import os

class MotionDetector(QObject):
    """Motion detection class using OpenCV"""
    
    motion_detected = pyqtSignal(str, dict)  # camera_id, motion_info
    motion_ended = pyqtSignal(str)  # camera_id
    
    def __init__(self):
        super().__init__()
        self.background_subtractors = {}
        self.previous_frames = {}
        self.motion_states = {}
        self.detection_zones = {}
        self.sensitivity = 50
        self.min_contour_area = 500
        self.motion_threshold = 25
        self.motion_history = {}
        self.alerts_path = "./alerts"
        
        self.ensure_directories()
        
    def ensure_directories(self):
        """Ensure alert directories exist"""
        os.makedirs(self.alerts_path, exist_ok=True)
        
    def initialize_detector(self, camera_id, detection_method="background_subtraction"):
        """Initialize motion detector for a camera"""
        if detection_method == "background_subtraction":
            # Use MOG2 background subtractor
            self.background_subtractors[camera_id] = cv2.createBackgroundSubtractorMOG2(
                detectShadows=True,
                varThreshold=16,
                history=500
            )
        
        self.motion_states[camera_id] = {
            'is_motion': False,
            'motion_start_time': None,
            'motion_duration': 0,
            'last_motion_time': None,
            'motion_count': 0
        }
        
        self.motion_history[camera_id] = []
        
        print(f"Motion detector initialized for camera {camera_id}")
        
    def set_detection_zone(self, camera_id, zone_points):
        """Set detection zone for a camera"""
        if len(zone_points) >= 3:
            self.detection_zones[camera_id] = np.array(zone_points, dtype=np.int32)
            print(f"Detection zone set for camera {camera_id}")
        
    def set_sensitivity(self, sensitivity):
        """Set motion detection sensitivity (0-100)"""
        self.sensitivity = max(0, min(100, sensitivity))
        self.motion_threshold = int(255 * (100 - self.sensitivity) / 100)
        
    def detect_motion(self, camera_id, frame):
        """Detect motion in frame"""
        if frame is None:
            return False, None
            
        try:
            # Initialize detector if not exists
            if camera_id not in self.background_subtractors:
                self.initialize_detector(camera_id)
                
            # Convert to grayscale
            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred_frame = cv2.GaussianBlur(gray_frame, (21, 21), 0)
            
            # Detect motion using background subtraction
            motion_detected, motion_info = self._detect_with_background_subtraction(
                camera_id, frame, blurred_frame
            )
            
            # Update motion state
            self._update_motion_state(camera_id, motion_detected, motion_info)
            
            return motion_detected, motion_info
            
        except Exception as e:
            print(f"Motion detection error for camera {camera_id}: {e}")
            return False, None
            
    def _detect_with_background_subtraction(self, camera_id, frame, gray_frame):
        """Detect motion using background subtraction"""
        bg_subtractor = self.background_subtractors[camera_id]
        
        # Apply background subtraction
        fg_mask = bg_subtractor.apply(gray_frame)
        
        # Remove shadows
        fg_mask[fg_mask == 127] = 0
        
        # Apply morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
        
        # Apply detection zone if set
        if camera_id in self.detection_zones:
            zone_mask = np.zeros_like(fg_mask)
            cv2.fillPoly(zone_mask, [self.detection_zones[camera_id]], 255)
            fg_mask = cv2.bitwise_and(fg_mask, zone_mask)
            
        # Find contours
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours by area
        significant_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
        
        motion_detected = len(significant_contours) > 0
        
        motion_info = {
            'timestamp': datetime.now(),
            'contours_count': len(significant_contours),
            'total_motion_area': sum(cv2.contourArea(c) for c in significant_contours),
            'bounding_boxes': [],
            'motion_mask': fg_mask,
            'motion_percentage': 0
        }
        
        if motion_detected:
            # Calculate bounding boxes
            for contour in significant_contours:
                x, y, w, h = cv2.boundingRect(contour)
                motion_info['bounding_boxes'].append((x, y, w, h))
                
            # Calculate motion percentage
            motion_pixels = cv2.countNonZero(fg_mask)
            total_pixels = fg_mask.shape[0] * fg_mask.shape[1]
            motion_info['motion_percentage'] = (motion_pixels / total_pixels) * 100
            
        return motion_detected, motion_info
        
    def _detect_with_frame_difference(self, camera_id, gray_frame):
        """Detect motion using frame difference method"""
        if camera_id not in self.previous_frames:
            self.previous_frames[camera_id] = gray_frame
            return False, None
            
        # Calculate frame difference
        frame_diff = cv2.absdiff(self.previous_frames[camera_id], gray_frame)
        
        # Apply threshold
        _, thresh = cv2.threshold(frame_diff, self.motion_threshold, 255, cv2.THRESH_BINARY)
        
        # Apply morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Apply detection zone if set
        if camera_id in self.detection_zones:
            zone_mask = np.zeros_like(thresh)
            cv2.fillPoly(zone_mask, [self.detection_zones[camera_id]], 255)
            thresh = cv2.bitwise_and(thresh, zone_mask)
            
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours by area
        significant_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
        
        motion_detected = len(significant_contours) > 0
        
        motion_info = {
            'timestamp': datetime.now(),
            'contours_count': len(significant_contours),
            'total_motion_area': sum(cv2.contourArea(c) for c in significant_contours),
            'bounding_boxes': [],
            'motion_mask': thresh,
            'motion_percentage': 0
        }
        
        if motion_detected:
            # Calculate bounding boxes
            for contour in significant_contours:
                x, y, w, h = cv2.boundingRect(contour)
                motion_info['bounding_boxes'].append((x, y, w, h))
                
            # Calculate motion percentage
            motion_pixels = cv2.countNonZero(thresh)
            total_pixels = thresh.shape[0] * thresh.shape[1]
            motion_info['motion_percentage'] = (motion_pixels / total_pixels) * 100
            
        # Update previous frame
        self.previous_frames[camera_id] = gray_frame.copy()
        
        return motion_detected, motion_info
        
    def _update_motion_state(self, camera_id, motion_detected, motion_info):
        """Update motion state for camera"""
        current_time = datetime.now()
        state = self.motion_states[camera_id]
        
        if motion_detected:
            if not state['is_motion']:
                # Motion started
                state['is_motion'] = True
                state['motion_start_time'] = current_time
                state['motion_count'] += 1
                
                # Emit motion detected signal
                self.motion_detected.emit(camera_id, motion_info)
                
                # Save motion alert
                self._save_motion_alert(camera_id, motion_info)
                
            state['last_motion_time'] = current_time
            state['motion_duration'] = (current_time - state['motion_start_time']).total_seconds()
            
        else:
            if state['is_motion']:
                # Check if motion has ended (no motion for 2 seconds)
                if state['last_motion_time'] and (current_time - state['last_motion_time']).total_seconds() > 2:
                    state['is_motion'] = False
                    state['motion_duration'] = (current_time - state['motion_start_time']).total_seconds()
                    
                    # Emit motion ended signal
                    self.motion_ended.emit(camera_id)
                    
        # Add to motion history
        self.motion_history[camera_id].append({
            'timestamp': current_time,
            'motion_detected': motion_detected,
            'motion_info': motion_info
        })
        
        # Keep only recent history (last 100 entries)
        if len(self.motion_history[camera_id]) > 100:
            self.motion_history[camera_id] = self.motion_history[camera_id][-100:]
            
    def _save_motion_alert(self, camera_id, motion_info):
        """Save motion alert to file"""
        try:
            timestamp = motion_info['timestamp']
            date_str = timestamp.strftime("%Y-%m-%d")
            time_str = timestamp.strftime("%H-%M-%S")
            
            # Create daily alerts directory
            daily_alerts_path = os.path.join(self.alerts_path, date_str)
            os.makedirs(daily_alerts_path, exist_ok=True)
            
            # Save alert info
            alert_info = {
                'camera_id': camera_id,
                'timestamp': timestamp.isoformat(),
                'contours_count': motion_info['contours_count'],
                'total_motion_area': motion_info['total_motion_area'],
                'motion_percentage': motion_info['motion_percentage'],
                'bounding_boxes': motion_info['bounding_boxes']
            }
            
            alert_filename = f"motion_alert_{camera_id}_{time_str}.json"
            alert_filepath = os.path.join(daily_alerts_path, alert_filename)
            
            with open(alert_filepath, 'w') as f:
                json.dump(alert_info, f, indent=2)
                
        except Exception as e:
            print(f"Failed to save motion alert: {e}")
            
    def draw_motion_overlay(self, frame, camera_id, motion_info):
        """Draw motion detection overlay on frame"""
        if motion_info is None:
            return frame
            
        overlay_frame = frame.copy()
        
        # Draw detection zone
        if camera_id in self.detection_zones:
            cv2.polylines(overlay_frame, [self.detection_zones[camera_id]], 
                         True, (0, 255, 255), 2)
            
        # Draw bounding boxes
        for x, y, w, h in motion_info['bounding_boxes']:
            cv2.rectangle(overlay_frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
            cv2.putText(overlay_frame, "MOTION", (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
        # Draw motion status
        state = self.motion_states.get(camera_id, {})
        if state.get('is_motion', False):
            cv2.putText(overlay_frame, "MOTION DETECTED", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            duration = state.get('motion_duration', 0)
            cv2.putText(overlay_frame, f"Duration: {duration:.1f}s", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
        return overlay_frame
        
    def get_motion_state(self, camera_id):
        """Get current motion state for camera"""
        return self.motion_states.get(camera_id, {})
        
    def get_motion_history(self, camera_id, limit=10):
        """Get motion history for camera"""
        history = self.motion_history.get(camera_id, [])
        return history[-limit:] if limit > 0 else history
        
    def clear_motion_history(self, camera_id):
        """Clear motion history for camera"""
        if camera_id in self.motion_history:
            self.motion_history[camera_id] = []
            
    def get_motion_statistics(self, camera_id):
        """Get motion detection statistics"""
        state = self.motion_states.get(camera_id, {})
        history = self.motion_history.get(camera_id, [])
        
        stats = {
            'total_motion_events': state.get('motion_count', 0),
            'current_motion_duration': state.get('motion_duration', 0),
            'is_motion_active': state.get('is_motion', False),
            'last_motion_time': state.get('last_motion_time'),
            'recent_motion_events': len([h for h in history[-50:] if h['motion_detected']]),
            'motion_frequency': 0
        }
        
        # Calculate motion frequency (events per hour)
        if len(history) > 1:
            time_span = (history[-1]['timestamp'] - history[0]['timestamp']).total_seconds() / 3600
            if time_span > 0:
                motion_events = len([h for h in history if h['motion_detected']])
                stats['motion_frequency'] = motion_events / time_span
                
        return stats
