"""
Video Recorder Module
Handles video recording functionality for camera streams
Supports automatic and manual recording with motion detection
"""

import cv2
import os
import threading
import time
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import json

class VideoRecorder(QObject):
    """Video recording manager"""
    
    recording_started = pyqtSignal(str)  # camera_id
    recording_stopped = pyqtSignal(str)  # camera_id
    recording_error = pyqtSignal(str, str)  # camera_id, error_message
    
    def __init__(self):
        super().__init__()
        self.recordings = {}  # camera_id -> recording_info
        self.video_writers = {}  # camera_id -> cv2.VideoWriter
        self.recording_threads = {}  # camera_id -> thread
        self.recordings_path = "./recordings"
        self.recording_format = "mp4"
        self.recording_quality = "high"
        self.max_recording_duration = 3600  # 1 hour in seconds
        
        self.ensure_directories()
        self.setup_recording_timer()
        
    def ensure_directories(self):
        """Ensure recording directories exist"""
        os.makedirs(self.recordings_path, exist_ok=True)
        
        # Create date-based subdirectories
        today = datetime.now().strftime("%Y-%m-%d")
        daily_path = os.path.join(self.recordings_path, today)
        os.makedirs(daily_path, exist_ok=True)
        
    def setup_recording_timer(self):
        """Setup timer for automatic recording management"""
        self.management_timer = QTimer()
        self.management_timer.timeout.connect(self.manage_recordings)
        self.management_timer.start(60000)  # Check every minute
        
    def get_recording_path(self, camera_id, camera_name):
        """Generate recording file path"""
        timestamp = datetime.now()
        date_str = timestamp.strftime("%Y-%m-%d")
        time_str = timestamp.strftime("%H-%M-%S")
        
        # Create camera-specific directory
        camera_dir = os.path.join(self.recordings_path, date_str, camera_name.replace(' ', '_'))
        os.makedirs(camera_dir, exist_ok=True)
        
        filename = f"{camera_name.replace(' ', '_')}_{time_str}.{self.recording_format}"
        return os.path.join(camera_dir, filename)
        
    def get_video_codec(self):
        """Get video codec based on format"""
        if self.recording_format == "mp4":
            return cv2.VideoWriter_fourcc(*'mp4v')
        elif self.recording_format == "avi":
            return cv2.VideoWriter_fourcc(*'XVID')
        else:
            return cv2.VideoWriter_fourcc(*'mp4v')
            
    def get_recording_resolution(self, frame):
        """Get recording resolution based on quality setting"""
        height, width = frame.shape[:2]
        
        if self.recording_quality == "high":
            return width, height
        elif self.recording_quality == "medium":
            return width // 2, height // 2
        elif self.recording_quality == "low":
            return width // 4, height // 4
        else:
            return width, height
            
    def start_recording(self, camera_id, camera_name="Unknown", frames_source=None):
        """Start recording for a specific camera"""
        try:
            if camera_id in self.recordings:
                print(f"Recording already active for camera {camera_id}")
                return False
                
            # Generate recording path
            recording_path = self.get_recording_path(camera_id, camera_name)
            
            # Create recording info
            recording_info = {
                'camera_id': camera_id,
                'camera_name': camera_name,
                'start_time': datetime.now(),
                'file_path': recording_path,
                'frames_written': 0,
                'duration': 0,
                'status': 'active'
            }
            
            self.recordings[camera_id] = recording_info
            
            # Start recording thread
            recording_thread = threading.Thread(
                target=self._recording_worker,
                args=(camera_id, frames_source),
                daemon=True
            )
            self.recording_threads[camera_id] = recording_thread
            recording_thread.start()
            
            self.recording_started.emit(camera_id)
            print(f"Started recording for camera {camera_id}: {recording_path}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to start recording: {str(e)}"
            print(error_msg)
            self.recording_error.emit(camera_id, error_msg)
            return False
            
    def stop_recording(self, camera_id):
        """Stop recording for a specific camera"""
        try:
            if camera_id not in self.recordings:
                print(f"No active recording for camera {camera_id}")
                return False
                
            # Mark recording as stopping
            self.recordings[camera_id]['status'] = 'stopping'
            
            # Wait for recording thread to finish
            if camera_id in self.recording_threads:
                self.recording_threads[camera_id].join(timeout=5)
                del self.recording_threads[camera_id]
                
            # Clean up video writer
            if camera_id in self.video_writers:
                self.video_writers[camera_id].release()
                del self.video_writers[camera_id]
                
            # Finalize recording info
            recording_info = self.recordings[camera_id]
            recording_info['end_time'] = datetime.now()
            recording_info['duration'] = (recording_info['end_time'] - recording_info['start_time']).total_seconds()
            recording_info['status'] = 'completed'
            
            # Save recording metadata
            self._save_recording_metadata(recording_info)
            
            del self.recordings[camera_id]
            
            self.recording_stopped.emit(camera_id)
            print(f"Stopped recording for camera {camera_id}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to stop recording: {str(e)}"
            print(error_msg)
            self.recording_error.emit(camera_id, error_msg)
            return False
            
    def _recording_worker(self, camera_id, frames_source):
        """Worker thread for recording video"""
        try:
            recording_info = self.recordings[camera_id]
            video_writer = None
            fps = 30
            
            while recording_info['status'] == 'active':
                # Get frame from source (this would be connected to camera manager)
                frame = self._get_frame_for_recording(camera_id, frames_source)
                
                if frame is not None:
                    # Initialize video writer on first frame
                    if video_writer is None:
                        width, height = self.get_recording_resolution(frame)
                        codec = self.get_video_codec()
                        
                        video_writer = cv2.VideoWriter(
                            recording_info['file_path'],
                            codec,
                            fps,
                            (width, height)
                        )
                        
                        if not video_writer.isOpened():
                            raise Exception("Failed to initialize video writer")
                            
                        self.video_writers[camera_id] = video_writer
                        
                    # Resize frame if needed
                    width, height = self.get_recording_resolution(frame)
                    if frame.shape[1] != width or frame.shape[0] != height:
                        frame = cv2.resize(frame, (width, height))
                        
                    # Write frame
                    video_writer.write(frame)
                    recording_info['frames_written'] += 1
                    
                    # Update duration
                    recording_info['duration'] = (datetime.now() - recording_info['start_time']).total_seconds()
                    
                    # Check maximum duration
                    if recording_info['duration'] >= self.max_recording_duration:
                        print(f"Maximum recording duration reached for camera {camera_id}")
                        break
                        
                # Control recording frame rate
                time.sleep(1/fps)
                
        except Exception as e:
            error_msg = f"Recording worker error: {str(e)}"
            print(error_msg)
            self.recording_error.emit(camera_id, error_msg)
        finally:
            if video_writer:
                video_writer.release()
                
    def _get_frame_for_recording(self, camera_id, frames_source):
        """Get frame for recording (placeholder - would be connected to camera manager)"""
        # This would be connected to the camera manager to get actual frames
        # For now, return None to prevent errors
        return None
        
    def _save_recording_metadata(self, recording_info):
        """Save recording metadata to JSON file"""
        try:
            metadata_path = recording_info['file_path'].replace(f".{self.recording_format}", "_metadata.json")
            
            metadata = {
                'camera_id': recording_info['camera_id'],
                'camera_name': recording_info['camera_name'],
                'start_time': recording_info['start_time'].isoformat(),
                'end_time': recording_info['end_time'].isoformat(),
                'duration_seconds': recording_info['duration'],
                'frames_written': recording_info['frames_written'],
                'file_path': recording_info['file_path'],
                'file_size_bytes': os.path.getsize(recording_info['file_path']) if os.path.exists(recording_info['file_path']) else 0
            }
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            print(f"Failed to save recording metadata: {e}")
            
    def start_all_recording(self):
        """Start recording for all cameras"""
        # This would be connected to camera manager to get all active cameras
        print("Start all recording requested")
        
    def stop_all_recording(self):
        """Stop recording for all cameras"""
        camera_ids = list(self.recordings.keys())
        for camera_id in camera_ids:
            self.stop_recording(camera_id)
            
    def is_recording(self, camera_id):
        """Check if camera is currently recording"""
        return camera_id in self.recordings and self.recordings[camera_id]['status'] == 'active'
        
    def get_recording_info(self, camera_id):
        """Get recording information for specific camera"""
        return self.recordings.get(camera_id, None)
        
    def get_all_recordings_info(self):
        """Get information about all active recordings"""
        return self.recordings.copy()
        
    def manage_recordings(self):
        """Manage recordings (cleanup, rotation, etc.)"""
        try:
            # Update recording durations
            for camera_id, recording_info in self.recordings.items():
                if recording_info['status'] == 'active':
                    recording_info['duration'] = (datetime.now() - recording_info['start_time']).total_seconds()
                    
            # Clean up old recordings if needed
            self._cleanup_old_recordings()
            
        except Exception as e:
            print(f"Recording management error: {e}")
            
    def _cleanup_old_recordings(self):
        """Clean up old recording files"""
        try:
            # This would implement cleanup logic based on retention settings
            # For now, just ensure directories exist
            self.ensure_directories()
            
        except Exception as e:
            print(f"Cleanup error: {e}")
            
    def get_recording_statistics(self):
        """Get recording statistics"""
        stats = {
            'active_recordings': len([r for r in self.recordings.values() if r['status'] == 'active']),
            'total_recordings_today': 0,
            'total_storage_used': 0,
            'average_recording_duration': 0
        }
        
        # Calculate statistics from recording files
        try:
            today = datetime.now().strftime("%Y-%m-%d")
            daily_path = os.path.join(self.recordings_path, today)
            
            if os.path.exists(daily_path):
                for root, dirs, files in os.walk(daily_path):
                    for file in files:
                        if file.endswith(f'.{self.recording_format}'):
                            stats['total_recordings_today'] += 1
                            file_path = os.path.join(root, file)
                            stats['total_storage_used'] += os.path.getsize(file_path)
                            
        except Exception as e:
            print(f"Statistics calculation error: {e}")
            
        return stats
