# Camera Monitoring System - Professional Edition

A comprehensive camera monitoring system built in Python with a professional glossy black interface. Supports multiple camera types including RTSP, IP, USB, and ONVIF cameras with advanced motion detection and recording capabilities.

## 🚀 Features

### ✅ Core Features
- **Multi-Camera Support**: RTSP, IP, USB, and ONVIF cameras
- **Professional Interface**: Glossy black theme with modern design
- **Multi-Language Support**: Arabic and English
- **Flexible Display**: 2x2, 2x4, 4x4 camera grid layouts
- **Real-time Monitoring**: Live video feeds with overlay information

### 🎥 Camera Features
- **RTSP Cameras**: Dahua, Hikvision, and other brands
- **IP Cameras**: HTTP/HTTPS streaming
- **USB Cameras**: Local webcams and USB cameras
- **ONVIF Support**: Standard ONVIF protocol
- **Multi-Channel**: Support for cameras with multiple streams

### 🔍 Motion Detection
- **OpenCV-based**: Advanced motion detection using CV2
- **Configurable Zones**: Define specific detection areas
- **Sensitivity Control**: Adjustable motion sensitivity (0-100)
- **Smart Alerts**: Motion-triggered notifications and recordings
- **Background Subtraction**: Professional motion detection algorithm

### 📹 Recording & Storage
- **Multiple Formats**: MP4, AVI video formats
- **Quality Settings**: High, Medium, Low recording quality
- **Automatic Recording**: Motion-triggered recording
- **Manual Recording**: On-demand recording control
- **Organized Storage**: Files organized by date/time/camera
- **Screenshot Capture**: Manual and scheduled screenshots

### ⚙️ Advanced Settings
- **Camera Management**: Easy add/remove/configure cameras
- **Detection Zones**: Custom motion detection areas
- **Recording Settings**: Quality, format, duration controls
- **Storage Management**: Automatic cleanup and retention
- **Network Settings**: Timeout and retry configurations

## 🛠️ Installation

### Prerequisites
- Windows 7/8/10/11
- Python 3.8+ (for development)
- Minimum 4GB RAM
- DirectX compatible graphics card

### Quick Start (EXE Version)
1. Download the latest release
2. Extract to desired folder
3. Run `CameraMonitoringSystem.exe`
4. Configure cameras through the interface

### Development Installation
```bash
# Clone the repository
git clone https://github.com/cameramonitoring/camera-monitoring-system.git
cd camera-monitoring-system

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

### Building EXE
```bash
# Build executable
python build_exe.py

# Executable will be created in dist/ folder
```

## 📋 Camera Configuration

### RTSP Cameras
```
rtsp://username:password@*************:554/cam/realmonitor?channel=1&subtype=0
```

### IP Cameras
```
http://*************:8080/video
https://*************:8443/video
```

### USB Cameras
```
0  # First USB camera
1  # Second USB camera
```

### ONVIF Cameras
```
rtsp://username:password@*************:554/onvif1
```

## 🎛️ Interface Overview

### Main Window
- **Video Grid**: Multi-camera display area
- **Side Panel**: Camera control and management
- **Menu Bar**: File, View, Recording menus
- **Status Bar**: System status and time display

### Side Panel Features
- **Camera List**: All configured cameras
- **Recording Controls**: Individual camera recording
- **System Info**: Status and time display
- **Quick Actions**: Add camera, take screenshots

### Settings Dialog
- **Camera Settings**: Add/edit camera configurations
- **Motion Detection**: Sensitivity and zone settings
- **Recording Options**: Quality, format, and storage
- **Display Settings**: Grid layout and overlay options

## 🔧 Configuration Files

### config.json
```json
{
  "app_settings": {
    "window_width": 1000,
    "window_height": 800,
    "language": "en",
    "theme": "dark",
    "motion_sensitivity": 50
  },
  "storage_settings": {
    "recordings_path": "./recordings",
    "screenshots_path": "./screenshots",
    "organize_by_date": true
  }
}
```

### cameras.json
```json
{
  "cameras": [
    {
      "id": "camera_1",
      "name": "Front Door",
      "type": "rtsp",
      "url": "rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0",
      "enabled": true,
      "recording_enabled": true,
      "motion_detection_enabled": true
    }
  ]
}
```

## 🚀 Usage Examples

### Adding RTSP Camera
1. Click "Add Camera" in side panel
2. Enter camera name: "Front Door"
3. Select type: "RTSP"
4. Enter URL: `rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0`
5. Enable motion detection and recording
6. Click "OK"

### Setting Up Motion Detection
1. Right-click on camera view
2. Select "Motion Settings"
3. Adjust sensitivity slider
4. Draw detection zone if needed
5. Enable motion recording

### Recording Management
- **Manual Recording**: Click "REC" button for specific camera
- **Motion Recording**: Enable in camera settings
- **All Cameras**: Use "Record All" button
- **Screenshots**: Click "Take Screenshots" for all cameras

## 📁 File Structure

```
camera-monitoring-system/
├── main.py                 # Main application entry point
├── camera_manager.py       # Camera connection and streaming
├── video_recorder.py       # Recording functionality
├── motion_detector.py      # Motion detection algorithms
├── settings_manager.py     # Configuration management
├── ui_components.py        # Custom UI widgets
├── build_exe.py           # EXE build script
├── config.json            # System configuration
├── cameras.json           # Camera configurations
├── requirements.txt       # Python dependencies
├── recordings/            # Video recordings
├── screenshots/           # Screenshot images
└── alerts/               # Motion detection alerts
```

## 🔍 Troubleshooting

### Common Issues

**Camera Not Connecting**
- Check camera URL and credentials
- Verify network connectivity
- Test with VLC media player first

**Motion Detection Not Working**
- Adjust sensitivity settings
- Check detection zone configuration
- Ensure adequate lighting

**Recording Issues**
- Check available disk space
- Verify write permissions
- Check recording format settings

**Performance Issues**
- Reduce number of simultaneous cameras
- Lower recording quality
- Close other applications

## 🛡️ Security Features

- **Secure Storage**: Encrypted configuration files
- **Access Control**: User permission system (future)
- **Network Security**: Secure RTSP connections
- **Data Protection**: Local storage only

## 🔄 Updates & Maintenance

### Automatic Features
- **Log Rotation**: Automatic cleanup of old logs
- **Storage Management**: Configurable retention policies
- **Performance Monitoring**: System resource tracking

### Manual Maintenance
- **Database Cleanup**: Remove old recordings
- **Configuration Backup**: Export/import settings
- **System Updates**: Check for new versions

## 📞 Support

### Documentation
- **User Manual**: Detailed usage instructions
- **API Reference**: Developer documentation
- **Video Tutorials**: Step-by-step guides

### Community
- **GitHub Issues**: Bug reports and feature requests
- **Discussion Forum**: Community support
- **Email Support**: Technical assistance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 🙏 Acknowledgments

- **OpenCV**: Computer vision library
- **PyQt5**: GUI framework
- **NumPy**: Numerical computing
- **Pillow**: Image processing
- **Community**: Contributors and testers

## 📊 System Requirements

### Minimum Requirements
- **OS**: Windows 7 SP1 or later
- **RAM**: 4GB
- **Storage**: 1GB free space
- **Network**: For IP cameras

### Recommended Requirements
- **OS**: Windows 10/11
- **RAM**: 8GB or more
- **Storage**: 10GB+ for recordings
- **GPU**: DirectX 11 compatible
- **Network**: Gigabit Ethernet for multiple HD cameras

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Developed by**: Camera Monitoring Solutions
