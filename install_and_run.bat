@echo off
echo ========================================
echo Camera Monitoring System - Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Install dependencies
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo.

REM Run system test
echo Running system test...
python test_system.py
if errorlevel 1 (
    echo.
    echo WARNING: Some tests failed. The system may not work properly.
    echo Do you want to continue anyway? (Y/N)
    set /p choice=
    if /i not "%choice%"=="Y" (
        echo Setup cancelled.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Choose an option:
echo 1. Run the application now
echo 2. Build EXE file
echo 3. Exit
echo.
set /p option=Enter your choice (1-3): 

if "%option%"=="1" (
    echo.
    echo Starting Camera Monitoring System...
    python main.py
) else if "%option%"=="2" (
    echo.
    echo Building EXE file...
    python build_exe.py
    if errorlevel 1 (
        echo Build failed. Please check the error messages above.
    ) else (
        echo.
        echo EXE file created successfully!
        echo Location: dist\CameraMonitoringSystem.exe
    )
) else (
    echo.
    echo You can run the application later with: python main.py
    echo Or build EXE with: python build_exe.py
)

echo.
pause
