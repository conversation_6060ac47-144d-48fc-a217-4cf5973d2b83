#!/usr/bin/env python3
"""
Camera Monitoring System
Professional camera monitoring application with glossy black interface
Supports RTSP, IP, USB cameras with motion detection and recording
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                            QFrame, QSplitter, QMenuBar, QStatusBar, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

from camera_manager import CameraManager
from video_recorder import VideoRecorder
from motion_detector import MotionDetector
from settings_manager import SettingsManager
from ui_components import CameraWidget, SidePanel, SettingsDialog

class CameraMonitoringSystem(QMainWindow):
    def __init__(self):
        super().__init__()
        self.settings_manager = SettingsManager()
        self.camera_manager = CameraManager()
        self.video_recorder = VideoRecorder()
        self.motion_detector = MotionDetector()
        
        # Load settings
        self.config = self.settings_manager.load_config()
        self.cameras_config = self.settings_manager.load_cameras()
        
        # Initialize UI
        self.init_ui()
        self.setup_style()
        self.setup_connections()
        
        # Start system
        self.start_monitoring()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Camera Monitoring System - Professional Edition")
        self.setGeometry(100, 100, 
                        self.config['app_settings']['window_width'],
                        self.config['app_settings']['window_height'])
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Create side panel
        self.side_panel = SidePanel(self.cameras_config)
        self.side_panel.setMaximumWidth(250)
        self.side_panel.setMinimumWidth(200)
        splitter.addWidget(self.side_panel)
        
        # Create video display area
        self.video_area = self.create_video_area()
        splitter.addWidget(self.video_area)
        
        # Set splitter proportions
        splitter.setSizes([250, 750])
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create status bar
        self.create_status_bar()
        
        # Setup timer for UI updates
        self.ui_timer = QTimer()
        self.ui_timer.timeout.connect(self.update_ui)
        self.ui_timer.start(1000)  # Update every second
        
    def create_video_area(self):
        """Create the video display area with grid layout"""
        video_widget = QWidget()
        video_widget.setStyleSheet("""
            QWidget {
                background-color: #1a1a1a;
                border: 1px solid #333333;
                border-radius: 5px;
            }
        """)
        
        # Create grid layout for cameras
        self.video_layout = QGridLayout(video_widget)
        self.video_layout.setSpacing(2)
        self.video_layout.setContentsMargins(5, 5, 5, 5)
        
        # Initialize camera widgets
        self.camera_widgets = {}
        self.setup_camera_grid()
        
        return video_widget
        
    def setup_camera_grid(self):
        """Setup the camera grid based on configuration"""
        grid_layout = self.config['display_settings']['grid_layout']
        
        if grid_layout == "2x2":
            rows, cols = 2, 2
        elif grid_layout == "2x4":
            rows, cols = 2, 4
        elif grid_layout == "4x4":
            rows, cols = 4, 4
        else:
            rows, cols = 2, 2
            
        # Clear existing widgets
        for widget in self.camera_widgets.values():
            widget.setParent(None)
        self.camera_widgets.clear()
        
        # Create camera widgets
        for row in range(rows):
            for col in range(cols):
                camera_widget = CameraWidget(f"Camera {row*cols + col + 1}")
                self.camera_widgets[f"{row}_{col}"] = camera_widget
                self.video_layout.addWidget(camera_widget, row, col)
                
    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        file_menu.addAction('Add Camera', self.add_camera)
        file_menu.addAction('Settings', self.open_settings)
        file_menu.addSeparator()
        file_menu.addAction('Exit', self.close)
        
        # View menu
        view_menu = menubar.addMenu('View')
        view_menu.addAction('2x2 Grid', lambda: self.change_grid_layout("2x2"))
        view_menu.addAction('2x4 Grid', lambda: self.change_grid_layout("2x4"))
        view_menu.addAction('4x4 Grid', lambda: self.change_grid_layout("4x4"))
        view_menu.addSeparator()
        view_menu.addAction('Fullscreen', self.toggle_fullscreen)
        
        # Recording menu
        record_menu = menubar.addMenu('Recording')
        record_menu.addAction('Start All Recording', self.start_all_recording)
        record_menu.addAction('Stop All Recording', self.stop_all_recording)
        record_menu.addAction('Take Screenshots', self.take_screenshots)
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = self.statusBar()
        
        # System status
        self.status_label = QLabel("System Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Recording status
        self.recording_label = QLabel("Recording: OFF")
        self.status_bar.addPermanentWidget(self.recording_label)
        
        # Time display
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
    def setup_style(self):
        """Setup the glossy black theme"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0d1117;
                color: #f0f6fc;
            }
            QMenuBar {
                background-color: #161b22;
                color: #f0f6fc;
                border-bottom: 1px solid #30363d;
                padding: 4px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #21262d;
            }
            QMenu {
                background-color: #161b22;
                color: #f0f6fc;
                border: 1px solid #30363d;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #21262d;
            }
            QStatusBar {
                background-color: #161b22;
                color: #f0f6fc;
                border-top: 1px solid #30363d;
            }
            QPushButton {
                background-color: #21262d;
                color: #f0f6fc;
                border: 1px solid #30363d;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #30363d;
                border-color: #58a6ff;
            }
            QPushButton:pressed {
                background-color: #1c2128;
            }
        """)
        
    def setup_connections(self):
        """Setup signal connections"""
        # Side panel connections
        self.side_panel.camera_selected.connect(self.on_camera_selected)
        self.side_panel.recording_toggled.connect(self.on_recording_toggled)
        
    def start_monitoring(self):
        """Start the camera monitoring system"""
        try:
            # Initialize cameras
            for camera_config in self.cameras_config['cameras']:
                if camera_config['enabled']:
                    self.camera_manager.add_camera(camera_config)
            
            # Start camera streams
            self.camera_manager.start_all_cameras()
            
            self.status_label.setText("Monitoring Active")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start monitoring: {str(e)}")
            
    def update_ui(self):
        """Update UI elements periodically"""
        # Update time display
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
        
        # Update camera frames
        self.update_camera_displays()
        
    def update_camera_displays(self):
        """Update camera display widgets with latest frames"""
        # This will be implemented to show live camera feeds
        pass
        
    def add_camera(self):
        """Open dialog to add new camera"""
        dialog = SettingsDialog(self)
        if dialog.exec_() == dialog.Accepted:
            # Add new camera configuration
            pass
            
    def open_settings(self):
        """Open settings dialog"""
        dialog = SettingsDialog(self)
        dialog.exec_()
        
    def change_grid_layout(self, layout):
        """Change the camera grid layout"""
        self.config['display_settings']['grid_layout'] = layout
        self.setup_camera_grid()
        self.settings_manager.save_config(self.config)
        
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
            
    def start_all_recording(self):
        """Start recording on all cameras"""
        self.video_recorder.start_all_recording()
        self.recording_label.setText("Recording: ON")
        
    def stop_all_recording(self):
        """Stop recording on all cameras"""
        self.video_recorder.stop_all_recording()
        self.recording_label.setText("Recording: OFF")
        
    def take_screenshots(self):
        """Take screenshots from all active cameras"""
        self.camera_manager.take_screenshots()
        self.status_label.setText("Screenshots saved")
        
    def on_camera_selected(self, camera_id):
        """Handle camera selection from side panel"""
        # Focus on selected camera
        pass
        
    def on_recording_toggled(self, camera_id, enabled):
        """Handle recording toggle for specific camera"""
        if enabled:
            self.video_recorder.start_recording(camera_id)
        else:
            self.video_recorder.stop_recording(camera_id)
            
    def closeEvent(self, event):
        """Handle application close event"""
        # Stop all cameras and recordings
        self.camera_manager.stop_all_cameras()
        self.video_recorder.stop_all_recording()
        
        # Save settings
        self.settings_manager.save_config(self.config)
        
        event.accept()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Camera Monitoring System")
    app.setApplicationVersion("1.0.0")
    
    # Set application icon (if available)
    # app.setWindowIcon(QIcon("icon.ico"))
    
    # Create and show main window
    window = CameraMonitoringSystem()
    window.show()
    
    # Start event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
