"""
Build Script for Camera Monitoring System
Creates executable file using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_directories():
    """Clean previous build directories"""
    directories_to_clean = ['build', 'dist', '__pycache__']
    
    for directory in directories_to_clean:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"Cleaned {directory} directory")
    
    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('cameras.json', '.'),
    ],
    hiddenimports=[
        'cv2',
        'numpy',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'imutils',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CameraMonitoringSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('camera_monitoring.spec', 'w') as f:
        f.write(spec_content.strip())
    
    print("Created PyInstaller spec file")

def create_version_info():
    """Create version info file for Windows executable"""
    version_info = '''
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Camera Monitoring Solutions'),
        StringStruct(u'FileDescription', u'Professional Camera Monitoring System'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'CameraMonitoringSystem'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'CameraMonitoringSystem.exe'),
        StringStruct(u'ProductName', u'Camera Monitoring System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w') as f:
        f.write(version_info.strip())
    
    print("Created version info file")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        return False

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    try:
        # Use the spec file for building
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'camera_monitoring.spec'
        ]
        
        subprocess.check_call(cmd)
        print("Executable built successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Failed to build executable: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            subprocess.check_call(cmd)
            print("Executable built successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to install PyInstaller or build executable: {e}")
            return False

def create_distribution_package():
    """Create distribution package with necessary files"""
    print("Creating distribution package...")
    
    dist_dir = Path('dist')
    package_dir = dist_dir / 'CameraMonitoringSystem_Package'
    
    # Create package directory
    package_dir.mkdir(exist_ok=True)
    
    # Copy executable
    exe_file = dist_dir / 'CameraMonitoringSystem.exe'
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir)
    
    # Copy configuration files
    config_files = ['config.json', 'cameras.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            shutil.copy2(config_file, package_dir)
    
    # Create directories
    directories = ['recordings', 'screenshots', 'alerts']
    for directory in directories:
        (package_dir / directory).mkdir(exist_ok=True)
    
    # Create README
    readme_content = """
# Camera Monitoring System - Professional Edition

## Installation
1. Extract all files to a folder
2. Run CameraMonitoringSystem.exe

## Features
- Support for RTSP, IP, USB, and ONVIF cameras
- Multi-camera grid view (2x2, 2x4, 4x4)
- Motion detection with configurable zones
- Automatic and manual recording
- Screenshot capture
- Professional glossy black interface
- Arabic and English language support

## Configuration
- Edit config.json for system settings
- Edit cameras.json to add/modify cameras
- Use the built-in settings dialog for easy configuration

## Camera URL Examples
- RTSP: rtsp://username:password@*************:554/cam/realmonitor?channel=1&subtype=0
- IP Camera: http://*************:8080/video
- USB Camera: 0 (for first USB camera, 1 for second, etc.)
- ONVIF: rtsp://username:password@*************:554/onvif1

## System Requirements
- Windows 7/8/10/11
- Minimum 4GB RAM
- DirectX compatible graphics card
- Network connection for IP cameras

## Support
For technical support and updates, visit our website.

Version 1.0.0
Copyright © 2024 Camera Monitoring Solutions
"""
    
    with open(package_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print(f"Distribution package created in: {package_dir}")

def main():
    """Main build process"""
    print("=" * 50)
    print("Camera Monitoring System - Build Script")
    print("=" * 50)
    
    # Clean previous builds
    clean_build_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("Build failed: Could not install dependencies")
        return False
    
    # Create build files
    create_spec_file()
    create_version_info()
    
    # Build executable
    if not build_executable():
        print("Build failed: Could not create executable")
        return False
    
    # Create distribution package
    create_distribution_package()
    
    print("=" * 50)
    print("Build completed successfully!")
    print("Executable location: dist/CameraMonitoringSystem.exe")
    print("Distribution package: dist/CameraMonitoringSystem_Package/")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
