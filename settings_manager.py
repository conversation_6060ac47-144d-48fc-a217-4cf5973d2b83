"""
Settings Manager <PERSON><PERSON><PERSON>les configuration management for the camera monitoring system
"""

import json
import os
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

class SettingsManager(QObject):
    """Settings management class"""
    
    settings_changed = pyqtSignal(dict)  # new_settings
    
    def __init__(self):
        super().__init__()
        self.config_file = "config.json"
        self.cameras_file = "cameras.json"
        self.default_config = self._get_default_config()
        self.default_cameras = self._get_default_cameras()
        
    def _get_default_config(self):
        """Get default configuration"""
        return {
            "app_settings": {
                "window_width": 1000,
                "window_height": 800,
                "language": "en",
                "theme": "dark",
                "auto_save_recordings": True,
                "recording_format": "mp4",
                "screenshot_format": "jpg",
                "motion_detection_enabled": True,
                "motion_sensitivity": 50,
                "recording_quality": "high"
            },
            "storage_settings": {
                "recordings_path": "./recordings",
                "screenshots_path": "./screenshots",
                "organize_by_date": True,
                "max_recording_duration": 3600,
                "auto_delete_old_files": False,
                "retention_days": 30
            },
            "display_settings": {
                "show_camera_name": True,
                "show_timestamp": True,
                "show_fps": True,
                "grid_layout": "2x2",
                "fullscreen_on_doubleclick": True
            },
            "motion_settings": {
                "sensitivity": 50,
                "min_contour_area": 500,
                "detection_method": "background_subtraction",
                "save_motion_alerts": True,
                "motion_recording_duration": 30
            },
            "network_settings": {
                "rtsp_timeout": 10,
                "connection_retry_attempts": 3,
                "connection_retry_delay": 5
            }
        }
        
    def _get_default_cameras(self):
        """Get default camera configuration"""
        return {
            "cameras": []
        }
        
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                merged_config = self._merge_configs(self.default_config, config)
                return merged_config
            else:
                # Create default config file
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.default_config.copy()
            
    def save_config(self, config):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.settings_changed.emit(config)
            return True
            
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
            
    def load_cameras(self):
        """Load camera configuration from file"""
        try:
            if os.path.exists(self.cameras_file):
                with open(self.cameras_file, 'r', encoding='utf-8') as f:
                    cameras = json.load(f)
                return cameras
            else:
                # Create default cameras file
                self.save_cameras(self.default_cameras)
                return self.default_cameras.copy()
                
        except Exception as e:
            print(f"Error loading cameras: {e}")
            return self.default_cameras.copy()
            
    def save_cameras(self, cameras):
        """Save camera configuration to file"""
        try:
            with open(self.cameras_file, 'w', encoding='utf-8') as f:
                json.dump(cameras, f, indent=2, ensure_ascii=False)
            return True
            
        except Exception as e:
            print(f"Error saving cameras: {e}")
            return False
            
    def add_camera(self, camera_config):
        """Add new camera to configuration"""
        try:
            cameras = self.load_cameras()
            
            # Generate unique ID if not provided
            if 'id' not in camera_config:
                camera_config['id'] = f"camera_{len(cameras['cameras']) + 1}"
                
            # Add default values
            default_camera = {
                'enabled': True,
                'recording_enabled': False,
                'motion_detection_enabled': True,
                'position': {'grid_x': 0, 'grid_y': 0}
            }
            
            for key, value in default_camera.items():
                if key not in camera_config:
                    camera_config[key] = value
                    
            cameras['cameras'].append(camera_config)
            
            if self.save_cameras(cameras):
                print(f"Added camera: {camera_config['id']}")
                return True
            return False
            
        except Exception as e:
            print(f"Error adding camera: {e}")
            return False
            
    def remove_camera(self, camera_id):
        """Remove camera from configuration"""
        try:
            cameras = self.load_cameras()
            cameras['cameras'] = [c for c in cameras['cameras'] if c['id'] != camera_id]
            
            if self.save_cameras(cameras):
                print(f"Removed camera: {camera_id}")
                return True
            return False
            
        except Exception as e:
            print(f"Error removing camera: {e}")
            return False
            
    def update_camera(self, camera_id, updates):
        """Update camera configuration"""
        try:
            cameras = self.load_cameras()
            
            for camera in cameras['cameras']:
                if camera['id'] == camera_id:
                    camera.update(updates)
                    break
            else:
                print(f"Camera {camera_id} not found")
                return False
                
            if self.save_cameras(cameras):
                print(f"Updated camera: {camera_id}")
                return True
            return False
            
        except Exception as e:
            print(f"Error updating camera: {e}")
            return False
            
    def get_camera(self, camera_id):
        """Get specific camera configuration"""
        cameras = self.load_cameras()
        for camera in cameras['cameras']:
            if camera['id'] == camera_id:
                return camera
        return None
        
    def _merge_configs(self, default, user):
        """Merge user config with default config"""
        merged = default.copy()
        
        for key, value in user.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key] = self._merge_configs(merged[key], value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
                
        return merged
        
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        try:
            self.save_config(self.default_config)
            self.save_cameras(self.default_cameras)
            print("Configuration reset to defaults")
            return True
            
        except Exception as e:
            print(f"Error resetting configuration: {e}")
            return False
            
    def export_settings(self, filepath):
        """Export settings to file"""
        try:
            config = self.load_config()
            cameras = self.load_cameras()
            
            export_data = {
                'config': config,
                'cameras': cameras,
                'export_timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
            print(f"Settings exported to: {filepath}")
            return True
            
        except Exception as e:
            print(f"Error exporting settings: {e}")
            return False
            
    def import_settings(self, filepath):
        """Import settings from file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
                
            if 'config' in import_data:
                self.save_config(import_data['config'])
                
            if 'cameras' in import_data:
                self.save_cameras(import_data['cameras'])
                
            print(f"Settings imported from: {filepath}")
            return True
            
        except Exception as e:
            print(f"Error importing settings: {e}")
            return False
            
    def validate_camera_config(self, camera_config):
        """Validate camera configuration"""
        required_fields = ['name', 'type', 'url']
        
        for field in required_fields:
            if field not in camera_config:
                return False, f"Missing required field: {field}"
                
        # Validate camera type
        valid_types = ['rtsp', 'ip', 'usb', 'onvif']
        if camera_config['type'] not in valid_types:
            return False, f"Invalid camera type: {camera_config['type']}"
            
        # Validate URL based on type
        if camera_config['type'] == 'usb':
            try:
                int(camera_config['url'])
            except ValueError:
                return False, "USB camera URL must be a number"
        elif camera_config['type'] in ['rtsp', 'ip', 'onvif']:
            if not camera_config['url'].startswith(('rtsp://', 'http://', 'https://')):
                return False, "Network camera URL must start with rtsp://, http://, or https://"
                
        return True, "Valid configuration"
        
    def get_setting(self, section, key, default=None):
        """Get specific setting value"""
        config = self.load_config()
        return config.get(section, {}).get(key, default)
        
    def set_setting(self, section, key, value):
        """Set specific setting value"""
        config = self.load_config()
        if section not in config:
            config[section] = {}
        config[section][key] = value
        return self.save_config(config)
        
    def get_all_settings(self):
        """Get all settings"""
        return {
            'config': self.load_config(),
            'cameras': self.load_cameras()
        }
        
    def backup_settings(self):
        """Create backup of current settings"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"settings_backup_{timestamp}.json"
            return self.export_settings(backup_filename)
            
        except Exception as e:
            print(f"Error creating backup: {e}")
            return False
