"""
UI Components Module
Custom UI widgets for the camera monitoring system
"""

import cv2
import numpy as np
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QScrollArea, QDialog,
                            QFormLayout, QLineEdit, QComboBox, QSpinBox,
                            QCheckBox, QTextEdit, QGroupBox, QGridLayout,
                            QSlider, QProgressBar, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QPixmap, QImage, QPainter, QFont, QIcon, QPalette, QColor

class CameraWidget(QWidget):
    """Widget for displaying camera feed"""
    
    double_clicked = pyqtSignal(str)  # camera_id
    
    def __init__(self, camera_name="Camera", camera_id=None):
        super().__init__()
        self.camera_name = camera_name
        self.camera_id = camera_id or camera_name
        self.current_frame = None
        self.is_recording = False
        self.motion_detected = False
        self.connection_status = False
        
        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # Video display label
        self.video_label = QLabel()
        self.video_label.setMinimumSize(320, 240)
        self.video_label.setScaledContents(True)
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("No Signal")
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #1a1a1a;
                border: 2px solid #333333;
                border-radius: 5px;
                color: #888888;
                font-size: 14px;
            }
        """)
        layout.addWidget(self.video_label)
        
        # Info panel
        info_layout = QHBoxLayout()
        info_layout.setContentsMargins(5, 2, 5, 2)
        
        # Camera name
        self.name_label = QLabel(self.camera_name)
        self.name_label.setFont(QFont("Arial", 10, QFont.Bold))
        info_layout.addWidget(self.name_label)
        
        info_layout.addStretch()
        
        # Status indicators
        self.status_label = QLabel("●")
        self.status_label.setFont(QFont("Arial", 12))
        info_layout.addWidget(self.status_label)
        
        self.recording_label = QLabel("REC")
        self.recording_label.setFont(QFont("Arial", 8, QFont.Bold))
        self.recording_label.setVisible(False)
        info_layout.addWidget(self.recording_label)
        
        layout.addLayout(info_layout)
        
        # Update status colors
        self.update_status_display()
        
    def setup_style(self):
        """Setup widget styling"""
        self.setStyleSheet("""
            CameraWidget {
                background-color: #161b22;
                border: 1px solid #30363d;
                border-radius: 8px;
            }
            QLabel {
                color: #f0f6fc;
            }
        """)
        
    def update_frame(self, frame):
        """Update the displayed frame"""
        if frame is not None:
            self.current_frame = frame
            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_image)
            
            self.video_label.setPixmap(pixmap)
            self.connection_status = True
        else:
            self.video_label.setText("No Signal")
            self.connection_status = False
            
        self.update_status_display()
        
    def set_recording_status(self, recording):
        """Set recording status"""
        self.is_recording = recording
        self.recording_label.setVisible(recording)
        self.update_status_display()
        
    def set_motion_status(self, motion):
        """Set motion detection status"""
        self.motion_detected = motion
        self.update_status_display()
        
    def update_status_display(self):
        """Update status indicator colors"""
        if not self.connection_status:
            self.status_label.setStyleSheet("color: #ff4444;")  # Red for disconnected
        elif self.motion_detected:
            self.status_label.setStyleSheet("color: #ffaa00;")  # Orange for motion
        else:
            self.status_label.setStyleSheet("color: #44ff44;")  # Green for connected
            
        if self.is_recording:
            self.recording_label.setStyleSheet("color: #ff4444; background-color: #330000; padding: 2px; border-radius: 3px;")
            
    def mouseDoubleClickEvent(self, event):
        """Handle double click event"""
        self.double_clicked.emit(self.camera_id)

class SidePanel(QWidget):
    """Side panel for camera management"""
    
    camera_selected = pyqtSignal(str)  # camera_id
    recording_toggled = pyqtSignal(str, bool)  # camera_id, enabled
    add_camera_requested = pyqtSignal()
    
    def __init__(self, cameras_config):
        super().__init__()
        self.cameras_config = cameras_config
        self.camera_items = {}
        
        self.init_ui()
        self.setup_style()
        self.populate_cameras()
        
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Title
        title_label = QLabel("Camera Control")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Add camera button
        self.add_button = QPushButton("+ Add Camera")
        self.add_button.clicked.connect(self.add_camera_requested.emit)
        layout.addWidget(self.add_button)
        
        # Camera list
        self.camera_list = QScrollArea()
        self.camera_list.setWidgetResizable(True)
        self.camera_list.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.camera_list_widget = QWidget()
        self.camera_list_layout = QVBoxLayout(self.camera_list_widget)
        self.camera_list_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_list_layout.setSpacing(2)
        
        self.camera_list.setWidget(self.camera_list_widget)
        layout.addWidget(self.camera_list)
        
        # Control buttons
        controls_group = QGroupBox("Recording Control")
        controls_layout = QVBoxLayout(controls_group)
        
        self.record_all_button = QPushButton("Record All")
        self.record_all_button.setCheckable(True)
        controls_layout.addWidget(self.record_all_button)
        
        self.screenshot_button = QPushButton("Take Screenshots")
        controls_layout.addWidget(self.screenshot_button)
        
        layout.addWidget(controls_group)
        
        # System info
        info_group = QGroupBox("System Info")
        info_layout = QVBoxLayout(info_group)
        
        self.status_label = QLabel("Status: Ready")
        info_layout.addWidget(self.status_label)
        
        self.time_label = QLabel()
        info_layout.addWidget(self.time_label)
        
        layout.addWidget(info_group)
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_time)
        self.update_timer.start(1000)
        
    def setup_style(self):
        """Setup panel styling"""
        self.setStyleSheet("""
            SidePanel {
                background-color: #0d1117;
                border-right: 1px solid #30363d;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #30363d;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #21262d;
                color: #f0f6fc;
                border: 1px solid #30363d;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #30363d;
                border-color: #58a6ff;
            }
            QPushButton:checked {
                background-color: #1f6feb;
                border-color: #58a6ff;
            }
        """)
        
    def populate_cameras(self):
        """Populate camera list"""
        # Clear existing items
        for item in self.camera_items.values():
            item.setParent(None)
        self.camera_items.clear()
        
        # Add cameras
        for camera in self.cameras_config.get('cameras', []):
            self.add_camera_item(camera)
            
    def add_camera_item(self, camera_config):
        """Add camera item to list"""
        camera_id = camera_config['id']
        
        # Create camera item widget
        item_widget = QFrame()
        item_widget.setFrameStyle(QFrame.Box)
        item_widget.setStyleSheet("""
            QFrame {
                background-color: #161b22;
                border: 1px solid #30363d;
                border-radius: 5px;
                margin: 2px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout(item_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
        
        # Camera name
        name_label = QLabel(camera_config['name'])
        name_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(name_label)
        
        # Camera type and status
        info_layout = QHBoxLayout()
        type_label = QLabel(camera_config['type'].upper())
        type_label.setStyleSheet("color: #7d8590; font-size: 9px;")
        info_layout.addWidget(type_label)
        
        info_layout.addStretch()
        
        status_label = QLabel("●")
        status_label.setStyleSheet("color: #44ff44;")
        info_layout.addWidget(status_label)
        
        layout.addLayout(info_layout)
        
        # Control buttons
        buttons_layout = QHBoxLayout()
        
        record_button = QPushButton("REC")
        record_button.setCheckable(True)
        record_button.setMaximumWidth(40)
        record_button.clicked.connect(lambda checked, cid=camera_id: self.recording_toggled.emit(cid, checked))
        buttons_layout.addWidget(record_button)
        
        select_button = QPushButton("View")
        select_button.setMaximumWidth(50)
        select_button.clicked.connect(lambda: self.camera_selected.emit(camera_id))
        buttons_layout.addWidget(select_button)
        
        layout.addLayout(buttons_layout)
        
        # Add to layout
        self.camera_list_layout.addWidget(item_widget)
        self.camera_items[camera_id] = item_widget
        
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"Time: {current_time}")

class SettingsDialog(QDialog):
    """Settings dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Camera Settings")
        self.setModal(True)
        self.resize(500, 600)
        
        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        
        # Camera settings
        camera_group = QGroupBox("Add New Camera")
        camera_layout = QFormLayout(camera_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter camera name")
        camera_layout.addRow("Name:", self.name_edit)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["RTSP", "IP", "USB", "ONVIF"])
        camera_layout.addRow("Type:", self.type_combo)
        
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("rtsp://user:pass@ip:port/path or camera index for USB")
        camera_layout.addRow("URL/Index:", self.url_edit)
        
        self.enabled_check = QCheckBox()
        self.enabled_check.setChecked(True)
        camera_layout.addRow("Enabled:", self.enabled_check)
        
        layout.addWidget(camera_group)
        
        # Motion detection settings
        motion_group = QGroupBox("Motion Detection")
        motion_layout = QFormLayout(motion_group)
        
        self.motion_enabled_check = QCheckBox()
        self.motion_enabled_check.setChecked(True)
        motion_layout.addRow("Enable Motion Detection:", self.motion_enabled_check)
        
        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setRange(0, 100)
        self.sensitivity_slider.setValue(50)
        motion_layout.addRow("Sensitivity:", self.sensitivity_slider)
        
        layout.addWidget(motion_group)
        
        # Recording settings
        recording_group = QGroupBox("Recording Settings")
        recording_layout = QFormLayout(recording_group)
        
        self.recording_enabled_check = QCheckBox()
        recording_layout.addRow("Enable Recording:", self.recording_enabled_check)
        
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["High", "Medium", "Low"])
        recording_layout.addRow("Quality:", self.quality_combo)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["MP4", "AVI"])
        recording_layout.addRow("Format:", self.format_combo)
        
        layout.addWidget(recording_group)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ok_button)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
        
    def setup_style(self):
        """Setup dialog styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: #0d1117;
                color: #f0f6fc;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #30363d;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox {
                background-color: #21262d;
                border: 1px solid #30363d;
                border-radius: 4px;
                padding: 6px;
                color: #f0f6fc;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #58a6ff;
            }
            QPushButton {
                background-color: #21262d;
                color: #f0f6fc;
                border: 1px solid #30363d;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #30363d;
                border-color: #58a6ff;
            }
            QCheckBox {
                color: #f0f6fc;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #30363d;
                border-radius: 3px;
                background-color: #21262d;
            }
            QCheckBox::indicator:checked {
                background-color: #1f6feb;
                border-color: #58a6ff;
            }
            QSlider::groove:horizontal {
                border: 1px solid #30363d;
                height: 6px;
                background: #21262d;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #58a6ff;
                border: 1px solid #30363d;
                width: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }
        """)
        
    def get_camera_config(self):
        """Get camera configuration from dialog"""
        return {
            'name': self.name_edit.text(),
            'type': self.type_combo.currentText().lower(),
            'url': self.url_edit.text(),
            'enabled': self.enabled_check.isChecked(),
            'recording_enabled': self.recording_enabled_check.isChecked(),
            'motion_detection_enabled': self.motion_enabled_check.isChecked()
        }
